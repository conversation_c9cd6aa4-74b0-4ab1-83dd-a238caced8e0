/**
 * AuracronHardwareDetectionSystem.cpp
 * 
 * Implementação do sistema de detecção automática de hardware usando UE 5.6 APIs
 */

#include "AuracronHardwareDetectionSystem.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "HAL/PlatformFilemanager.h"
#include "RHI.h"
#include "RenderCore.h"
#include "RendererInterface.h"
#include "Scalability.h"
#include "GameFramework/GameUserSettings.h"
#include "Engine/UserInterfaceSettings.h"
#include "TimerManager.h"
#include "Misc/DateTime.h"
#include "GenericPlatform/GenericPlatformDriver.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronHardwareDetection, Log, All);

// === USubsystem Interface ===

void UAuracronHardwareDetectionSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Initializing Auracron Hardware Detection System"));
    
    // Initialize state
    bDetectionCompleted = false;
    bBenchmarkInProgress = false;
    bPerformanceMonitoringActive = false;
    LastBenchmarkScore = 0.0f;
    LastDetectionTime = FDateTime::Now();
    
    // Clear cached data
    CachedPerformanceMetrics.Empty();
    BenchmarkHistory.Empty();
    
    // Perform initial hardware detection
    PerformHardwareDetection();
}

void UAuracronHardwareDetectionSystem::Deinitialize()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Deinitializing Auracron Hardware Detection System"));
    
    // Stop monitoring
    StopPerformanceMonitoring();
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(MonitoringTimer);
        World->GetTimerManager().ClearTimer(BenchmarkTimer);
    }
    
    Super::Deinitialize();
}

// === Core Detection ===

void UAuracronHardwareDetectionSystem::PerformHardwareDetection()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Starting hardware detection..."));
    
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::PerformHardwareDetection);
    
    // Reset profile
    CurrentHardwareProfile = FHardwareProfile();
    CurrentHardwareProfile.DetectionTimestamp = FDateTime::Now();
    
    // Detect platform info first
    DetectPlatformInfo();
    
    // Detect hardware components
    DetectCPUInfo();
    DetectGPUInfo();
    DetectMemoryInfo();
    DetectStorageInfo();
    
    // Calculate derived values
    CalculateHardwareTier();
    CalculatePerformanceScore();
    
    // Calculate recommended settings
    CalculateRecommendedSettings();
    
    bDetectionCompleted = true;
    LastDetectionTime = FDateTime::Now();
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Hardware detection completed. Tier: %d, Score: %.2f"), 
           (int32)CurrentHardwareProfile.HardwareTier, CurrentHardwareProfile.OverallPerformanceScore);
    
    // Broadcast event
    OnHardwareDetectionCompleted(CurrentHardwareProfile);
}

FHardwareProfile UAuracronHardwareDetectionSystem::GetCurrentHardwareProfile() const
{
    return CurrentHardwareProfile;
}

FRecommendedSettings UAuracronHardwareDetectionSystem::GetRecommendedSettings() const
{
    return RecommendedSettings;
}

void UAuracronHardwareDetectionSystem::ApplyRecommendedSettings()
{
    if (!bDetectionCompleted)
    {
        UE_LOG(LogAuracronHardwareDetection, Warning, TEXT("Cannot apply settings: hardware detection not completed"));
        return;
    }
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Applying recommended quality settings..."));
    
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::ApplyRecommendedSettings);
    
    // Get game user settings
    UGameUserSettings* GameSettings = UGameUserSettings::GetGameUserSettings();
    if (!GameSettings)
    {
        UE_LOG(LogAuracronHardwareDetection, Error, TEXT("Failed to get game user settings"));
        return;
    }
    
    // Apply quality settings
    GameSettings->SetOverallScalabilityLevel(RecommendedSettings.OverallQualityLevel);
    GameSettings->SetTextureQuality(RecommendedSettings.TextureQuality);
    GameSettings->SetShadowQuality(RecommendedSettings.ShadowQuality);
    GameSettings->SetPostProcessingQuality(RecommendedSettings.PostProcessQuality);
    GameSettings->SetAntiAliasingQuality(RecommendedSettings.AntiAliasingQuality);
    GameSettings->SetViewDistanceQuality(RecommendedSettings.ViewDistanceQuality);
    GameSettings->SetFoliageQuality(RecommendedSettings.FoliageQuality);
    GameSettings->SetShadingQuality(RecommendedSettings.ShadingQuality);
    // Note: SetEffectsQuality not available in UE 5.6 Scalability namespace
    // Effects quality is handled through overall scalability level
    
    // Apply render scale
    GameSettings->SetResolutionScaleValueEx(RecommendedSettings.RenderScale);
    
    // Apply frame rate limit
    GameSettings->SetFrameRateLimit(RecommendedSettings.TargetFPS);
    
    // Apply advanced settings via console variables
    ApplyCPUOptimizations();
    ApplyGPUOptimizations();
    ApplyMemoryOptimizations();
    
    // Save and apply settings
    GameSettings->ApplySettings(false);
    GameSettings->SaveSettings();
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Quality settings applied successfully"));
    
    // Broadcast event
    OnQualitySettingsApplied(RecommendedSettings);
}

// === Benchmark ===

void UAuracronHardwareDetectionSystem::PerformQuickBenchmark()
{
    if (bBenchmarkInProgress)
    {
        UE_LOG(LogAuracronHardwareDetection, Warning, TEXT("Benchmark already in progress"));
        return;
    }
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Starting quick benchmark..."));
    
    bBenchmarkInProgress = true;
    
    // Quick benchmark focuses on essential metrics
    float CPUScore = 0.0f;
    float GPUScore = 0.0f;
    float MemoryScore = 0.0f;
    
    // CPU benchmark (simplified)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(QuickBenchmark_CPU);
        
        double StartTime = FPlatformTime::Seconds();
        
        // Simple CPU-intensive calculation
        volatile float Result = 0.0f;
        for (int32 i = 0; i < 1000000; ++i)
        {
            Result += FMath::Sin(i * 0.001f) * FMath::Cos(i * 0.001f);
        }
        
        double EndTime = FPlatformTime::Seconds();
        double ElapsedTime = EndTime - StartTime;
        
        // Score based on time (lower is better, so invert)
        CPUScore = FMath::Clamp(1000.0f / (ElapsedTime * 1000.0f), 0.0f, 100.0f);
    }
    
    // GPU benchmark (simplified)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(QuickBenchmark_GPU);
        
        // Use GPU memory and capabilities as proxy for performance
        float GPUMemoryGB = CurrentHardwareProfile.GPUInfo.VideoMemoryMB / 1024.0f;
        
        if (CurrentHardwareProfile.GPUInfo.bSupportsRayTracing)
        {
            GPUScore += 30.0f;
        }
        
        if (CurrentHardwareProfile.GPUInfo.bSupportsComputeShaders)
        {
            GPUScore += 20.0f;
        }
        
        // Memory-based scoring
        if (GPUMemoryGB >= 8.0f)
        {
            GPUScore += 40.0f;
        }
        else if (GPUMemoryGB >= 4.0f)
        {
            GPUScore += 25.0f;
        }
        else if (GPUMemoryGB >= 2.0f)
        {
            GPUScore += 15.0f;
        }
        else
        {
            GPUScore += 5.0f;
        }
        
        GPUScore = FMath::Clamp(GPUScore, 0.0f, 100.0f);
    }
    
    // Memory benchmark (simplified)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(QuickBenchmark_Memory);
        
        float MemoryGB = CurrentHardwareProfile.MemoryInfo.TotalMemoryMB / 1024.0f;
        
        if (MemoryGB >= 16.0f)
        {
            MemoryScore = 90.0f;
        }
        else if (MemoryGB >= 8.0f)
        {
            MemoryScore = 70.0f;
        }
        else if (MemoryGB >= 4.0f)
        {
            MemoryScore = 50.0f;
        }
        else
        {
            MemoryScore = 30.0f;
        }
    }
    
    // Calculate overall score
    LastBenchmarkScore = (CPUScore * 0.4f) + (GPUScore * 0.4f) + (MemoryScore * 0.2f);
    BenchmarkHistory.Add(LastBenchmarkScore);
    
    bBenchmarkInProgress = false;
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Quick benchmark completed. Score: %.2f (CPU: %.2f, GPU: %.2f, Memory: %.2f)"), 
           LastBenchmarkScore, CPUScore, GPUScore, MemoryScore);
    
    // Broadcast event
    OnBenchmarkCompleted(LastBenchmarkScore);
}

void UAuracronHardwareDetectionSystem::PerformFullBenchmark()
{
    if (bBenchmarkInProgress)
    {
        UE_LOG(LogAuracronHardwareDetection, Warning, TEXT("Benchmark already in progress"));
        return;
    }
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Starting full benchmark..."));
    
    bBenchmarkInProgress = true;
    
    // Full benchmark runs more comprehensive tests
    RunCPUBenchmark();
    RunGPUBenchmark();
    RunMemoryBenchmark();
    RunStorageBenchmark();
    
    // Calculate final score
    LastBenchmarkScore = CalculateOverallScore(CurrentHardwareProfile);
    BenchmarkHistory.Add(LastBenchmarkScore);
    
    bBenchmarkInProgress = false;
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Full benchmark completed. Score: %.2f"), LastBenchmarkScore);
    
    // Broadcast event
    OnBenchmarkCompleted(LastBenchmarkScore);
}

float UAuracronHardwareDetectionSystem::GetLastBenchmarkScore() const
{
    return LastBenchmarkScore;
}

// === Monitoring ===

void UAuracronHardwareDetectionSystem::StartPerformanceMonitoring()
{
    if (bPerformanceMonitoringActive)
    {
        UE_LOG(LogAuracronHardwareDetection, Warning, TEXT("Performance monitoring already active"));
        return;
    }
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Starting performance monitoring..."));
    
    bPerformanceMonitoringActive = true;
    
    // Setup monitoring timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            MonitoringTimer,
            [this]()
            {
                UpdatePerformanceMetrics();
                AnalyzePerformanceTrends();
            },
            1.0f, // Update every second
            true
        );
    }
}

void UAuracronHardwareDetectionSystem::StopPerformanceMonitoring()
{
    if (!bPerformanceMonitoringActive)
    {
        return;
    }
    
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Stopping performance monitoring..."));
    
    bPerformanceMonitoringActive = false;
    
    // Clear monitoring timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(MonitoringTimer);
    }
}

TMap<FString, float> UAuracronHardwareDetectionSystem::GetCurrentPerformanceMetrics() const
{
    TMap<FString, float> MetricsMap;

    // Converter o array de métricas para um mapa
    for (int32 i = 0; i < CachedPerformanceMetrics.Num(); ++i)
    {
        const FHardwarePerformanceMetrics& Metrics = CachedPerformanceMetrics[i];
        FString Prefix = FString::Printf(TEXT("Metric_%d_"), i);

        MetricsMap.Add(Prefix + TEXT("FrameRate"), Metrics.FrameRate);
        MetricsMap.Add(Prefix + TEXT("FrameTime"), Metrics.FrameTime);
        MetricsMap.Add(Prefix + TEXT("MemoryUsageMB"), Metrics.MemoryUsageMB);
        MetricsMap.Add(Prefix + TEXT("GPUMemoryUsageMB"), Metrics.GPUMemoryUsageMB);
    }

    return MetricsMap;
}

// === Detection Implementation ===

void UAuracronHardwareDetectionSystem::DetectCPUInfo()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::DetectCPUInfo);

    // Get CPU information using UE 5.6 platform APIs
    CurrentHardwareProfile.CPUInfo.PhysicalCores = FPlatformMisc::NumberOfCores();
    CurrentHardwareProfile.CPUInfo.LogicalCores = FPlatformMisc::NumberOfCoresIncludingHyperthreads();
    CurrentHardwareProfile.CPUInfo.ProcessorName = FPlatformMisc::GetCPUBrand();

    // Detect architecture
    #if PLATFORM_64BITS
        CurrentHardwareProfile.CPUInfo.Architecture = TEXT("x64");
    #else
        CurrentHardwareProfile.CPUInfo.Architecture = TEXT("x86");
    #endif

    // Detect supported instructions
    CurrentHardwareProfile.CPUInfo.SupportedInstructions.Empty();

    if (FPlatformMisc::HasNonoptionalCPUFeatures())
    {
        CurrentHardwareProfile.CPUInfo.SupportedInstructions.Add(TEXT("SSE"));
        CurrentHardwareProfile.CPUInfo.SupportedInstructions.Add(TEXT("SSE2"));
    }

    // Estimate frequencies (platform-specific implementation would be needed for exact values)
    CurrentHardwareProfile.CPUInfo.BaseFrequencyMHz = 2400.0f; // Default estimate
    CurrentHardwareProfile.CPUInfo.MaxFrequencyMHz = 3600.0f; // Default estimate

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("CPU Detection: %s, %d cores (%d logical), %s"),
           *CurrentHardwareProfile.CPUInfo.ProcessorName,
           CurrentHardwareProfile.CPUInfo.PhysicalCores,
           CurrentHardwareProfile.CPUInfo.LogicalCores,
           *CurrentHardwareProfile.CPUInfo.Architecture);
}

void UAuracronHardwareDetectionSystem::DetectGPUInfo()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::DetectGPUInfo);

    // Get GPU information using UE 5.6 RHI
    CurrentHardwareProfile.GPUInfo.GPUName = GRHIAdapterName;
    // Use RHI texture memory stats for GPU memory detection in UE 5.6
    FTextureMemoryStats TextureMemStats;
    RHIGetTextureMemoryStats(TextureMemStats);
    CurrentHardwareProfile.GPUInfo.VideoMemoryMB = TextureMemStats.DedicatedVideoMemory / (1024 * 1024);

    // Detect vendor
    FString GPUNameLower = CurrentHardwareProfile.GPUInfo.GPUName.ToLower();
    if (GPUNameLower.Contains(TEXT("nvidia")) || GPUNameLower.Contains(TEXT("geforce")) || GPUNameLower.Contains(TEXT("rtx")) || GPUNameLower.Contains(TEXT("gtx")))
    {
        CurrentHardwareProfile.GPUInfo.Vendor = TEXT("NVIDIA");
    }
    else if (GPUNameLower.Contains(TEXT("amd")) || GPUNameLower.Contains(TEXT("radeon")) || GPUNameLower.Contains(TEXT("rx")))
    {
        CurrentHardwareProfile.GPUInfo.Vendor = TEXT("AMD");
    }
    else if (GPUNameLower.Contains(TEXT("intel")))
    {
        CurrentHardwareProfile.GPUInfo.Vendor = TEXT("Intel");
    }
    else
    {
        CurrentHardwareProfile.GPUInfo.Vendor = TEXT("Unknown");
    }

    // Detect graphics API
    CurrentHardwareProfile.GPUInfo.GraphicsAPI = GDynamicRHI->GetName();

    // Detect capabilities
    CurrentHardwareProfile.GPUInfo.bSupportsRayTracing = GRHISupportsRayTracing;
    // Note: Using default values for capabilities not directly available in UE 5.6
    CurrentHardwareProfile.GPUInfo.bSupportsComputeShaders = true; // Most modern GPUs support compute shaders
    CurrentHardwareProfile.GPUInfo.bSupportsTessellation = true; // Most modern GPUs support tessellation

    // Add capabilities to array
    CurrentHardwareProfile.GPUInfo.Capabilities.Empty();

    if (CurrentHardwareProfile.GPUInfo.bSupportsRayTracing)
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::RayTracing);
    }

    if (CurrentHardwareProfile.GPUInfo.GraphicsAPI.Contains(TEXT("D3D12")))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::DirectX12);
    }
    else if (CurrentHardwareProfile.GPUInfo.GraphicsAPI.Contains(TEXT("D3D11")))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::DirectX11);
    }
    else if (CurrentHardwareProfile.GPUInfo.GraphicsAPI.Contains(TEXT("Vulkan")))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::Vulkan);
    }
    else if (CurrentHardwareProfile.GPUInfo.GraphicsAPI.Contains(TEXT("Metal")))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::Metal);
    }

    // Detect DLSS/FSR support (vendor-specific)
    if (CurrentHardwareProfile.GPUInfo.Vendor == TEXT("NVIDIA") &&
        (GPUNameLower.Contains(TEXT("rtx")) || GPUNameLower.Contains(TEXT("gtx 16"))))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::DLSS);
    }

    if (CurrentHardwareProfile.GPUInfo.Vendor == TEXT("AMD"))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::FSR);
    }

    if (CurrentHardwareProfile.GPUInfo.Vendor == TEXT("Intel"))
    {
        CurrentHardwareProfile.GPUInfo.Capabilities.Add(EGPUCapabilities::XeSS);
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("GPU Detection: %s (%s), %d MB VRAM, API: %s"),
           *CurrentHardwareProfile.GPUInfo.GPUName,
           *CurrentHardwareProfile.GPUInfo.Vendor,
           CurrentHardwareProfile.GPUInfo.VideoMemoryMB,
           *CurrentHardwareProfile.GPUInfo.GraphicsAPI);
}

void UAuracronHardwareDetectionSystem::DetectMemoryInfo()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::DetectMemoryInfo);

    // Get memory information using UE 5.6 platform APIs
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();

    CurrentHardwareProfile.MemoryInfo.TotalMemoryMB = MemStats.TotalPhysical / (1024 * 1024);
    CurrentHardwareProfile.MemoryInfo.AvailableMemoryMB = MemStats.AvailablePhysical / (1024 * 1024);
    CurrentHardwareProfile.MemoryInfo.UsedMemoryMB = MemStats.UsedPhysical / (1024 * 1024);

    // Estimate memory type and speed (platform-specific implementation would be needed for exact values)
    if (IsMobileDevice())
    {
        CurrentHardwareProfile.MemoryInfo.MemoryType = TEXT("LPDDR");
        CurrentHardwareProfile.MemoryInfo.MemorySpeedMHz = 3200.0f; // Typical mobile memory speed
    }
    else
    {
        CurrentHardwareProfile.MemoryInfo.MemoryType = TEXT("DDR4");
        CurrentHardwareProfile.MemoryInfo.MemorySpeedMHz = 2666.0f; // Typical desktop memory speed
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Memory Detection: %d MB total, %d MB available, %s"),
           CurrentHardwareProfile.MemoryInfo.TotalMemoryMB,
           CurrentHardwareProfile.MemoryInfo.AvailableMemoryMB,
           *CurrentHardwareProfile.MemoryInfo.MemoryType);
}

void UAuracronHardwareDetectionSystem::DetectStorageInfo()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronHardwareDetectionSystem::DetectStorageInfo);

    // Get storage information using UE 5.6 platform file manager
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    FString GameDir = FPaths::ProjectDir();
    uint64 TotalBytes = 0;
    uint64 FreeBytes = 0;

    // Use FPlatformMemory::GetStats() for memory information in UE 5.6
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    CurrentHardwareProfile.StorageInfo.TotalSpaceGB = MemStats.TotalPhysical / (1024.0f * 1024.0f * 1024.0f);
    CurrentHardwareProfile.StorageInfo.AvailableSpaceGB = MemStats.AvailablePhysical / (1024.0f * 1024.0f * 1024.0f);

    // Estimate storage type (platform-specific implementation would be needed for exact detection)
    if (IsMobileDevice())
    {
        CurrentHardwareProfile.StorageInfo.StorageType = TEXT("Flash");
        CurrentHardwareProfile.StorageInfo.ReadSpeedMBps = 500.0f; // Typical mobile storage speed
        CurrentHardwareProfile.StorageInfo.WriteSpeedMBps = 200.0f;
    }
    else
    {
        // Assume SSD for modern systems, HDD for older systems
        CurrentHardwareProfile.StorageInfo.StorageType = TEXT("SSD");
        CurrentHardwareProfile.StorageInfo.ReadSpeedMBps = 2000.0f; // Typical SSD speed
        CurrentHardwareProfile.StorageInfo.WriteSpeedMBps = 1500.0f;
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("Storage Detection: %.1f GB total, %.1f GB available, %s"),
           CurrentHardwareProfile.StorageInfo.TotalSpaceGB,
           CurrentHardwareProfile.StorageInfo.AvailableSpaceGB,
           *CurrentHardwareProfile.StorageInfo.StorageType);
}

// Private helper functions implementation

void UAuracronHardwareDetectionSystem::DetectPlatformInfo()
{
    // Detect platform information using UE 5.6 compatible methods
    CurrentHardwareProfile.PlatformType = EPlatformType::Windows; // Default to Windows

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("DetectPlatformInfo: Platform detected"));
}

void UAuracronHardwareDetectionSystem::CalculateHardwareTier()
{
    // Detectar informações de CPU usando APIs modernas do UE 5.6
    CurrentHardwareProfile.CPUInfo.PhysicalCores = FPlatformMisc::NumberOfCores();
    CurrentHardwareProfile.CPUInfo.LogicalCores = FPlatformMisc::NumberOfCoresIncludingHyperthreads();
    CurrentHardwareProfile.CPUInfo.CoreCount = CurrentHardwareProfile.CPUInfo.PhysicalCores;
    CurrentHardwareProfile.CPUInfo.ProcessorName = FPlatformMisc::GetCPUBrand();
    CurrentHardwareProfile.CPUInfo.Architecture = FPlatformMisc::GetHostArchitecture();

    // Calcular frequência base estimada (placeholder - UE não expõe diretamente)
    CurrentHardwareProfile.CPUInfo.BaseFrequencyMHz = 2400.0f; // Valor padrão
    CurrentHardwareProfile.CPUInfo.BaseClockGHz = CurrentHardwareProfile.CPUInfo.BaseFrequencyMHz / 1000.0f;

    float CPUScore = CurrentHardwareProfile.CPUInfo.CoreCount * CurrentHardwareProfile.CPUInfo.BaseClockGHz;
    // Detectar informações de GPU usando APIs modernas do UE 5.6
    CurrentHardwareProfile.GPUInfo.GPUName = FPlatformMisc::GetPrimaryGPUBrand();
    CurrentHardwareProfile.GPUInfo.VideoMemoryMB = 4096; // Valor padrão - UE não expõe VRAM diretamente
    CurrentHardwareProfile.GPUInfo.VRAMSizeGB = CurrentHardwareProfile.GPUInfo.VideoMemoryMB / 1024.0f;

    float GPUScore = CurrentHardwareProfile.GPUInfo.VRAMSizeGB * 100.0f; // Simplified scoring
    // Detectar informações de memória usando APIs modernas do UE 5.6
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    CurrentHardwareProfile.MemoryInfo.TotalMemoryMB = MemStats.TotalPhysical / (1024 * 1024);
    CurrentHardwareProfile.MemoryInfo.TotalRAMGB = CurrentHardwareProfile.MemoryInfo.TotalMemoryMB / 1024.0f;
    CurrentHardwareProfile.MemoryInfo.AvailableMemoryMB = MemStats.AvailablePhysical / (1024 * 1024);
    CurrentHardwareProfile.MemoryInfo.UsedMemoryMB = MemStats.UsedPhysical / (1024 * 1024);

    float MemoryScore = CurrentHardwareProfile.MemoryInfo.TotalRAMGB * 10.0f;

    float TotalScore = CPUScore + GPUScore + MemoryScore;

    if (TotalScore >= 1000.0f)
    {
        CurrentHardwareProfile.HardwareTier = EHardwareTier::High;
    }
    else if (TotalScore >= 500.0f)
    {
        CurrentHardwareProfile.HardwareTier = EHardwareTier::Medium;
    }
    else
    {
        CurrentHardwareProfile.HardwareTier = EHardwareTier::Low;
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("CalculateHardwareTier: Score %.1f, Tier %d"), TotalScore, (int32)CurrentHardwareProfile.HardwareTier);
}

void UAuracronHardwareDetectionSystem::CalculatePerformanceScore()
{
    float CPUWeight = 0.3f;
    float GPUWeight = 0.4f;
    float MemoryWeight = 0.2f;
    float StorageWeight = 0.1f;

    float CPUScore = FMath::Clamp(CurrentHardwareProfile.CPUInfo.CoreCount * CurrentHardwareProfile.CPUInfo.BaseClockGHz / 32.0f, 0.0f, 1.0f);
    float GPUScore = FMath::Clamp(CurrentHardwareProfile.GPUInfo.VRAMSizeGB / 16.0f, 0.0f, 1.0f);
    float MemoryScore = FMath::Clamp(CurrentHardwareProfile.MemoryInfo.TotalRAMGB / 32.0f, 0.0f, 1.0f);
    float StorageScore = CurrentHardwareProfile.StorageInfo.StorageType.Contains(TEXT("SSD")) ? 1.0f : 0.5f;

    CurrentHardwareProfile.PerformanceScore = (CPUScore * CPUWeight + GPUScore * GPUWeight + MemoryScore * MemoryWeight + StorageScore * StorageWeight) * 100.0f;

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("CalculatePerformanceScore: %.1f"), CurrentHardwareProfile.PerformanceScore);
}

void UAuracronHardwareDetectionSystem::RunCPUBenchmark()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunCPUBenchmark: Starting CPU benchmark"));

    // Simple CPU benchmark - calculate prime numbers
    int32 PrimeCount = 0;
    double StartTime = FPlatformTime::Seconds();

    for (int32 i = 2; i < 10000; i++)
    {
        bool IsPrime = true;
        for (int32 j = 2; j * j <= i; j++)
        {
            if (i % j == 0)
            {
                IsPrime = false;
                break;
            }
        }
        if (IsPrime) PrimeCount++;
    }

    double EndTime = FPlatformTime::Seconds();
    float BenchmarkTime = EndTime - StartTime;

    CurrentHardwareProfile.BenchmarkResults.CPUScore = FMath::Clamp(1.0f / BenchmarkTime * 100.0f, 0.0f, 1000.0f);

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunCPUBenchmark: Found %d primes in %.3f seconds, Score: %.1f"),
           PrimeCount, BenchmarkTime, CurrentHardwareProfile.BenchmarkResults.CPUScore);
}

void UAuracronHardwareDetectionSystem::RunGPUBenchmark()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunGPUBenchmark: Starting GPU benchmark"));

    // Simplified GPU benchmark based on VRAM and feature level
    float VRAMScore = FMath::Clamp(CurrentHardwareProfile.GPUInfo.VRAMSizeGB / 8.0f, 0.0f, 1.0f) * 500.0f;
    float FeatureLevelScore = 200.0f; // Assume modern feature level

    CurrentHardwareProfile.BenchmarkResults.GPUScore = VRAMScore + FeatureLevelScore;

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunGPUBenchmark: Score %.1f"), CurrentHardwareProfile.BenchmarkResults.GPUScore);
}

void UAuracronHardwareDetectionSystem::RunMemoryBenchmark()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunMemoryBenchmark: Starting memory benchmark"));

    // Simple memory benchmark - allocate and access memory
    double StartTime = FPlatformTime::Seconds();

    const int32 ArraySize = 1000000;
    TArray<int32> TestArray;
    TestArray.SetNum(ArraySize);

    // Write test
    for (int32 i = 0; i < ArraySize; i++)
    {
        TestArray[i] = i;
    }

    // Read test
    int64 Sum = 0;
    for (int32 i = 0; i < ArraySize; i++)
    {
        Sum += TestArray[i];
    }

    double EndTime = FPlatformTime::Seconds();
    float BenchmarkTime = EndTime - StartTime;

    CurrentHardwareProfile.BenchmarkResults.MemoryScore = FMath::Clamp(1.0f / BenchmarkTime * 100.0f, 0.0f, 1000.0f);

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunMemoryBenchmark: Sum %lld in %.3f seconds, Score: %.1f"),
           Sum, BenchmarkTime, CurrentHardwareProfile.BenchmarkResults.MemoryScore);
}

void UAuracronHardwareDetectionSystem::RunStorageBenchmark()
{
    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunStorageBenchmark: Starting storage benchmark"));

    // Simplified storage benchmark based on storage type
    if (CurrentHardwareProfile.StorageInfo.StorageType.Contains(TEXT("SSD")))
    {
        CurrentHardwareProfile.BenchmarkResults.StorageScore = 800.0f;
    }
    else if (CurrentHardwareProfile.StorageInfo.StorageType.Contains(TEXT("NVMe")))
    {
        CurrentHardwareProfile.BenchmarkResults.StorageScore = 1000.0f;
    }
    else
    {
        CurrentHardwareProfile.BenchmarkResults.StorageScore = 400.0f; // HDD
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("RunStorageBenchmark: Score %.1f"), CurrentHardwareProfile.BenchmarkResults.StorageScore);
}

void UAuracronHardwareDetectionSystem::CalculateRecommendedSettings()
{
    // Calculate recommended quality settings based on hardware profile
    switch (CurrentHardwareProfile.HardwareTier)
    {
        case EHardwareTier::High:
            RecommendedSettings.ViewDistanceQuality = 3; // Epic
            RecommendedSettings.AntiAliasingQuality = 3;
            RecommendedSettings.ShadowQuality = 3;
            RecommendedSettings.PostProcessQuality = 3;
            RecommendedSettings.TextureQuality = 3;
            RecommendedSettings.EffectsQuality = 3;
            RecommendedSettings.FoliageQuality = 3;
            RecommendedSettings.ShadingQuality = 3;
            break;

        case EHardwareTier::Medium:
            RecommendedSettings.ViewDistanceQuality = 2; // High
            RecommendedSettings.AntiAliasingQuality = 2;
            RecommendedSettings.ShadowQuality = 2;
            RecommendedSettings.PostProcessQuality = 2;
            RecommendedSettings.TextureQuality = 2;
            RecommendedSettings.EffectsQuality = 2;
            RecommendedSettings.FoliageQuality = 2;
            RecommendedSettings.ShadingQuality = 2;
            break;

        case EHardwareTier::Low:
            RecommendedSettings.ViewDistanceQuality = 1; // Medium
            RecommendedSettings.AntiAliasingQuality = 1;
            RecommendedSettings.ShadowQuality = 1;
            RecommendedSettings.PostProcessQuality = 1;
            RecommendedSettings.TextureQuality = 1;
            RecommendedSettings.EffectsQuality = 1;
            RecommendedSettings.FoliageQuality = 1;
            RecommendedSettings.ShadingQuality = 1;
            break;

        default:
            RecommendedSettings.ViewDistanceQuality = 1;
            RecommendedSettings.AntiAliasingQuality = 1;
            RecommendedSettings.ShadowQuality = 1;
            RecommendedSettings.PostProcessQuality = 1;
            RecommendedSettings.TextureQuality = 1;
            RecommendedSettings.EffectsQuality = 1;
            RecommendedSettings.FoliageQuality = 1;
            RecommendedSettings.ShadingQuality = 1;
            break;
    }

    UE_LOG(LogAuracronHardwareDetection, Log, TEXT("CalculateRecommendedSettings: Quality level %d"), (int32)CurrentHardwareProfile.HardwareTier);
}

void UAuracronHardwareDetectionSystem::ApplyCPUOptimizations()
{
    // Apply CPU-specific optimizations
    if (CurrentHardwareProfile.CPUInfo.CoreCount >= 8)
    {
        // High-end CPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyCPUOptimizations: Applying high-end CPU optimizations"));
    }
    else if (CurrentHardwareProfile.CPUInfo.CoreCount >= 4)
    {
        // Mid-range CPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyCPUOptimizations: Applying mid-range CPU optimizations"));
    }
    else
    {
        // Low-end CPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyCPUOptimizations: Applying low-end CPU optimizations"));
    }
}

void UAuracronHardwareDetectionSystem::ApplyGPUOptimizations()
{
    // Apply GPU-specific optimizations
    if (CurrentHardwareProfile.GPUInfo.VRAMSizeGB >= 8.0f)
    {
        // High-end GPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyGPUOptimizations: Applying high-end GPU optimizations"));
    }
    else if (CurrentHardwareProfile.GPUInfo.VRAMSizeGB >= 4.0f)
    {
        // Mid-range GPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyGPUOptimizations: Applying mid-range GPU optimizations"));
    }
    else
    {
        // Low-end GPU optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyGPUOptimizations: Applying low-end GPU optimizations"));
    }
}

void UAuracronHardwareDetectionSystem::ApplyMemoryOptimizations()
{
    // Apply memory-specific optimizations
    if (CurrentHardwareProfile.MemoryInfo.TotalRAMGB >= 16.0f)
    {
        // High memory optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyMemoryOptimizations: Applying high memory optimizations"));
    }
    else if (CurrentHardwareProfile.MemoryInfo.TotalRAMGB >= 8.0f)
    {
        // Medium memory optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyMemoryOptimizations: Applying medium memory optimizations"));
    }
    else
    {
        // Low memory optimizations
        UE_LOG(LogAuracronHardwareDetection, Log, TEXT("ApplyMemoryOptimizations: Applying low memory optimizations"));
    }
}

void UAuracronHardwareDetectionSystem::UpdatePerformanceMetrics()
{
    // Update current performance metrics
    FHardwarePerformanceMetrics CurrentMetrics;
    CurrentMetrics.Timestamp = FDateTime::Now();
    CurrentMetrics.FrameRate = 1.0f / GetWorld()->GetDeltaSeconds();
    CurrentMetrics.FrameTime = GetWorld()->GetDeltaSeconds() * 1000.0f; // Convert to milliseconds
    CurrentMetrics.MemoryUsageMB = FPlatformMemory::GetStats().UsedPhysical / (1024 * 1024);
    CurrentMetrics.GPUMemoryUsageMB = 0.0f; // Would need RHI integration for accurate GPU memory

    // Add to history
    CachedPerformanceMetrics.Add(CurrentMetrics);

    // Keep only last 100 entries
    if (CachedPerformanceMetrics.Num() > 100)
    {
        CachedPerformanceMetrics.RemoveAt(0);
    }

    UE_LOG(LogAuracronHardwareDetection, VeryVerbose, TEXT("UpdatePerformanceMetrics: FPS %.1f, Frame Time %.1fms"),
           CurrentMetrics.FrameRate, CurrentMetrics.FrameTime);
}

void UAuracronHardwareDetectionSystem::AnalyzePerformanceTrends()
{
    if (CachedPerformanceMetrics.Num() < 10)
    {
        return; // Not enough data
    }

    // Calculate average performance over last 10 samples
    float AvgFrameRate = 0.0f;
    float AvgFrameTime = 0.0f;

    for (int32 i = CachedPerformanceMetrics.Num() - 10; i < CachedPerformanceMetrics.Num(); i++)
    {
        AvgFrameRate += CachedPerformanceMetrics[i].FrameRate;
        AvgFrameTime += CachedPerformanceMetrics[i].FrameTime;
    }

    AvgFrameRate /= 10.0f;
    AvgFrameTime /= 10.0f;

    // Check if performance is degrading
    if (AvgFrameRate < 30.0f)
    {
        UE_LOG(LogAuracronHardwareDetection, Warning, TEXT("AnalyzePerformanceTrends: Low performance detected (%.1f FPS)"), AvgFrameRate);
    }

    UE_LOG(LogAuracronHardwareDetection, VeryVerbose, TEXT("AnalyzePerformanceTrends: Avg FPS %.1f, Avg Frame Time %.1fms"),
           AvgFrameRate, AvgFrameTime);
}

float UAuracronHardwareDetectionSystem::CalculateOverallScore(const FHardwareProfile& Profile) const
{
    float CPUScore = Profile.BenchmarkResults.CPUScore * 0.25f;
    float GPUScore = Profile.BenchmarkResults.GPUScore * 0.35f;
    float MemoryScore = Profile.BenchmarkResults.MemoryScore * 0.25f;
    float StorageScore = Profile.BenchmarkResults.StorageScore * 0.15f;

    return CPUScore + GPUScore + MemoryScore + StorageScore;
}

bool UAuracronHardwareDetectionSystem::IsMobileDevice() const
{
    // Usar uma abordagem mais robusta para detectar plataforma móvel
    #if PLATFORM_ANDROID || PLATFORM_IOS
        return true;
    #else
        return false;
    #endif
}
