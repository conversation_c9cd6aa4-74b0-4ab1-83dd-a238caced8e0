/**
 * AuracronDynamicWeatherSystem.cpp
 * 
 * Implementation of advanced dynamic weather system that creates immersive
 * weather effects responding to gameplay events, player actions, and match
 * progression.
 * 
 * Uses UE 5.6 modern weather, VFX, audio, and lighting frameworks for
 * production-ready dynamic weather experiences.
 */

#include "AuracronDynamicWeatherSystem.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronPrismalFlow.h"
#include "AuracronPrismalIsland.h"
#include "Engine/World.h"
#include "Engine/DirectionalLight.h"
#include "Engine/SkyLight.h"
#include "Engine/ExponentialHeightFog.h"
#include "EngineUtils.h"
#include "Components/LightComponent.h"
// UE 5.6 Compatible - CharacterMovementComponent include
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/ExponentialHeightFogComponent.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"

void UAuracronDynamicWeatherSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // Initialize dynamic weather system using UE 5.6 subsystem initialization
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Dynamic Weather System"));

    // Initialize default configuration
    bWeatherSystemEnabled = true;
    WeatherUpdateFrequency = 2.0f;
    WeatherTransitionSpeed = 1.0f;
    bEnableGameplayEffects = true;
    bEnablePerformanceOptimization = true;

    // Initialize state
    bIsInitialized = false;
    LastWeatherUpdateTime = 0.0f;
    LastTransitionTime = 0.0f;
    LastPerformanceOptimization = 0.0f;
    TotalWeatherChanges = 0;

    // Initialize weather state
    GlobalWeatherState = FAuracronWeatherState();
    GlobalWeatherState.CurrentWeather.WeatherType = EDynamicWeatherType::Clear;
    GlobalWeatherState.CurrentWeather.Intensity = EWeatherIntensity::Light;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic Weather System initialized"));
}

void UAuracronDynamicWeatherSystem::Deinitialize()
{
    // Cleanup dynamic weather system using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deinitializing Dynamic Weather System"));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Remove all weather effects
    for (auto& VFXPair : ActiveWeatherVFX)
    {
        if (VFXPair.Value && IsValid(VFXPair.Value))
        {
            VFXPair.Value->DestroyComponent();
        }
    }

    for (auto& AudioPair : ActiveWeatherAudio)
    {
        if (AudioPair.Value && IsValid(AudioPair.Value))
        {
            AudioPair.Value->DestroyComponent();
        }
    }

    // Clear all data
    LayerWeatherStates.Empty();
    WeatherConfigurations.Empty();
    ActiveWeatherVFX.Empty();
    ActiveWeatherAudio.Empty();
    PlayerWeatherEffects.Empty();
    TriggerWeatherTypes.Empty();
    TriggerProbabilities.Empty();
    ActiveTriggers.Empty();

    bIsInitialized = false;

    Super::Deinitialize();
}

// === Core Weather Management Implementation ===

void UAuracronDynamicWeatherSystem::InitializeWeatherSystem()
{
    if (bIsInitialized || !bWeatherSystemEnabled)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing dynamic weather generation system..."));

    // Cache subsystem reference
    CachedRealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>();

    // Cache lighting references
    CacheLightingReferences();

    // Initialize weather configurations
    InitializeWeatherConfigurations();

    // Setup weather triggers
    SetupWeatherTriggers();

    // Start weather updates
    StartWeatherUpdates();

    // Initialize layer weather states
    InitializeLayerWeatherStates();

    // Integrate with other systems
    IntegrateWithRealmEvolution();
    IntegrateWithFlowSystem();
    IntegrateWithIslandSystem();

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dynamic weather system initialized successfully"));
}

void UAuracronDynamicWeatherSystem::UpdateWeatherSystem(float DeltaTime)
{
    if (!bIsInitialized || !bWeatherSystemEnabled)
    {
        return;
    }

    // Update weather system using UE 5.6 update system
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    LastWeatherUpdateTime = CurrentTime;

    // Process weather transitions
    ProcessWeatherTransitions(DeltaTime);

    // Update global weather
    UpdateGlobalWeather(DeltaTime);

    // Update layer-specific weather
    for (auto& LayerWeatherPair : LayerWeatherStates)
    {
        EAuracronRealmLayer Layer = LayerWeatherPair.Key;
        UpdateLayerWeather(Layer, DeltaTime);
    }

    // Update weather based on realm state
    UpdateWeatherBasedOnRealmState();

    // Update weather based on player activity
    UpdateWeatherBasedOnPlayerActivity();

    // Performance optimization
    if (bEnablePerformanceOptimization)
    {
        OptimizeWeatherPerformance();
    }
}

void UAuracronDynamicWeatherSystem::ChangeWeather(EDynamicWeatherType NewWeatherType, EWeatherIntensity Intensity, float Duration)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Change weather using UE 5.6 weather transition system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Changing weather to %s (Intensity: %s, Duration: %.1f)"), 
        *UEnum::GetValueAsString(NewWeatherType), *UEnum::GetValueAsString(Intensity), Duration);

    // Store previous weather
    GlobalWeatherState.PreviousWeatherType = GlobalWeatherState.CurrentWeather.WeatherType;

    // Create new weather configuration
    FAuracronDynamicWeatherConfig NewWeatherConfig;
    if (const FAuracronDynamicWeatherConfig* ConfigTemplate = WeatherConfigurations.Find(NewWeatherType))
    {
        NewWeatherConfig = *ConfigTemplate;
    }
    else
    {
        NewWeatherConfig.WeatherType = NewWeatherType;
    }

    NewWeatherConfig.Intensity = Intensity;
    NewWeatherConfig.Duration = Duration;

    // Start transition
    GlobalWeatherState.bIsTransitioning = true;
    GlobalWeatherState.TransitionProgress = 0.0f;
    GlobalWeatherState.CurrentWeather = NewWeatherConfig;
    GlobalWeatherState.WeatherStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Trigger weather change event
    OnWeatherChanged(GlobalWeatherState.PreviousWeatherType, NewWeatherType);

    TotalWeatherChanges++;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather change initiated"));
}

void UAuracronDynamicWeatherSystem::TriggerWeatherEvent(EWeatherTriggerEvent TriggerEvent, const FVector& Location)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Trigger weather event using UE 5.6 event system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Triggering weather event %s at %s"), 
        *UEnum::GetValueAsString(TriggerEvent), *Location.ToString());

    // Check trigger probability
    float* TriggerProbability = TriggerProbabilities.Find(TriggerEvent);
    if (TriggerProbability && FMath::RandRange(0.0f, 1.0f) > *TriggerProbability)
    {
        return; // Event not triggered
    }

    // Generate weather for event
    EDynamicWeatherType EventWeather = GenerateWeatherForEvent(TriggerEvent);
    EWeatherIntensity EventIntensity = CalculateWeatherIntensity(EventWeather, Location);
    float EventDuration = CalculateWeatherDuration(EventWeather, EventIntensity);

    // Apply weather change
    ChangeWeather(EventWeather, EventIntensity, EventDuration);

    // Add to active triggers
    ActiveTriggers.AddUnique(TriggerEvent);

    // Trigger event
    OnWeatherEventTriggered(TriggerEvent, Location);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather event triggered - Weather: %s, Intensity: %s"), 
        *UEnum::GetValueAsString(EventWeather), *UEnum::GetValueAsString(EventIntensity));
}

FAuracronWeatherState UAuracronDynamicWeatherSystem::GetCurrentWeatherState() const
{
    return GlobalWeatherState;
}

// === Layer-Specific Weather Implementation ===

void UAuracronDynamicWeatherSystem::SetLayerWeather(EAuracronRealmLayer Layer, EDynamicWeatherType WeatherType, EWeatherIntensity Intensity)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Set weather for specific layer using UE 5.6 layer weather system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting %s layer weather to %s (Intensity: %s)"), 
        *UEnum::GetValueAsString(Layer), *UEnum::GetValueAsString(WeatherType), *UEnum::GetValueAsString(Intensity));

    // Get or create layer weather state
    FAuracronLayerWeatherState& LayerWeatherState = LayerWeatherStates.FindOrAdd(Layer);

    // Create weather configuration
    FAuracronDynamicWeatherConfig LayerWeatherConfig;
    if (const FAuracronDynamicWeatherConfig* ConfigTemplate = WeatherConfigurations.Find(WeatherType))
    {
        LayerWeatherConfig = *ConfigTemplate;
    }
    else
    {
        LayerWeatherConfig.WeatherType = WeatherType;
    }

    LayerWeatherConfig.Intensity = Intensity;
    LayerWeatherConfig.AffectedLayers = {Layer};

    // Update layer weather state
    LayerWeatherState.CurrentWeatherType = WeatherType;
    // Convert EWeatherIntensity to float
    switch (Intensity)
    {
        case EWeatherIntensity::Light:
            LayerWeatherState.Intensity = 0.25f;
            break;
        case EWeatherIntensity::Moderate:
            LayerWeatherState.Intensity = 0.5f;
            break;
        case EWeatherIntensity::Heavy:
            LayerWeatherState.Intensity = 0.75f;
            break;
        case EWeatherIntensity::Extreme:
            LayerWeatherState.Intensity = 1.0f;
            break;
        default:
            LayerWeatherState.Intensity = 0.5f;
            break;
    }
    LayerWeatherState.LastUpdateTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    LayerWeatherState.bIsActive = true;

    // Apply weather effects to layer
    ApplyWeatherVFX(Layer, LayerWeatherConfig);
    ApplyWeatherAudio(Layer, LayerWeatherConfig);
    ApplyWeatherLighting(Layer, LayerWeatherConfig);
    ApplyWeatherFog(Layer, LayerWeatherConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer weather applied"));
}

FAuracronDynamicWeatherConfig UAuracronDynamicWeatherSystem::GetLayerWeather(EAuracronRealmLayer Layer) const
{
    if (const FAuracronLayerWeatherState* LayerWeatherState = LayerWeatherStates.Find(Layer))
    {
        // Convert layer weather state to weather config
        FAuracronDynamicWeatherConfig WeatherConfig;
        WeatherConfig.WeatherType = LayerWeatherState->CurrentWeatherType;

        // Convert float intensity to enum
        if (LayerWeatherState->Intensity <= 0.25f)
            WeatherConfig.Intensity = EWeatherIntensity::Light;
        else if (LayerWeatherState->Intensity <= 0.5f)
            WeatherConfig.Intensity = EWeatherIntensity::Moderate;
        else if (LayerWeatherState->Intensity <= 0.75f)
            WeatherConfig.Intensity = EWeatherIntensity::Heavy;
        else
            WeatherConfig.Intensity = EWeatherIntensity::Extreme;

        return WeatherConfig;
    }
    
    // Return global weather as fallback
    return GlobalWeatherState.CurrentWeather;
}

void UAuracronDynamicWeatherSystem::UpdateLayerWeatherEffects(EAuracronRealmLayer Layer, float DeltaTime)
{
    // Update layer weather effects using UE 5.6 layer effect system
    FAuracronLayerWeatherState* LayerWeatherState = LayerWeatherStates.Find(Layer);
    if (!LayerWeatherState)
    {
        return;
    }

    // Update transition progress
    // Update weather state based on time
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float TimeSinceUpdate = CurrentTime - LayerWeatherState->LastUpdateTime;

    if (TimeSinceUpdate > 5.0f) // Update every 5 seconds
    {
        LayerWeatherState->LastUpdateTime = CurrentTime;
        OnWeatherTransitionComplete(LayerWeatherState->CurrentWeatherType);
    }

    // Update weather VFX
    if (UNiagaraComponent* WeatherVFX = ActiveWeatherVFX.FindRef(Layer))
    {
        if (WeatherVFX && WeatherVFX->IsActive())
        {
            float IntensityMultiplier = LayerWeatherState->Intensity;
            float TransitionMultiplier = LayerWeatherState->bIsActive ? 1.0f : 0.5f;
            
            // UE 5.6 Compatible - Using FName variant for Niagara variables
            WeatherVFX->SetVariableFloat(FName("WeatherIntensity"), IntensityMultiplier * TransitionMultiplier);
            WeatherVFX->SetVariableFloat(FName("TransitionProgress"), TransitionMultiplier);
        }
    }

    // Update weather audio
    if (UAudioComponent* WeatherAudio = ActiveWeatherAudio.FindRef(Layer))
    {
        if (WeatherAudio && WeatherAudio->IsPlaying())
        {
            float IntensityMultiplier = LayerWeatherState->Intensity;
            float TransitionMultiplier = LayerWeatherState->bIsActive ? 1.0f : 0.5f;
            
            WeatherAudio->SetVolumeMultiplier(IntensityMultiplier * TransitionMultiplier);
        }
    }
}

// === Gameplay Integration Implementation ===

void UAuracronDynamicWeatherSystem::ApplyWeatherEffectsToPlayer(APawn* Player)
{
    if (!Player || !bEnableGameplayEffects)
    {
        return;
    }

    // Apply weather effects to player using UE 5.6 effect application
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying weather effects to player %s"), *Player->GetName());

    // Get player's current layer
    EAuracronRealmLayer PlayerLayer = CachedRealmSubsystem ? CachedRealmSubsystem->GetActorLayer(Player) : EAuracronRealmLayer::Terrestrial;

    // Get weather for player's layer
    FAuracronDynamicWeatherConfig LayerWeather = GetLayerWeather(PlayerLayer);

    // Apply weather-specific effects
    ApplyWeatherMovementEffects(Player, LayerWeather.WeatherType, GetWeatherIntensityMultiplier(LayerWeather.Intensity));
    ApplyWeatherVisibilityEffects(Player, LayerWeather.WeatherType, GetWeatherIntensityMultiplier(LayerWeather.Intensity));
    ApplyWeatherCombatEffects(Player, LayerWeather.WeatherType, GetWeatherIntensityMultiplier(LayerWeather.Intensity));

    // Apply gameplay effects
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        TArray<FActiveGameplayEffectHandle> PlayerEffects;

        for (TSubclassOf<UGameplayEffect> WeatherEffectClass : LayerWeather.WeatherEffects)
        {
            if (WeatherEffectClass)
            {
                const UGameplayEffect* WeatherEffect = WeatherEffectClass.GetDefaultObject();
                if (WeatherEffect)
                {
                    FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
                    EffectContext.AddSourceObject(this);

                    FGameplayEffectSpec WeatherSpec(WeatherEffect, EffectContext, GetWeatherIntensityMultiplier(LayerWeather.Intensity));
                WeatherSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

                    FActiveGameplayEffectHandle EffectHandle = ASC->ApplyGameplayEffectSpecToSelf(WeatherSpec);
                    PlayerEffects.Add(EffectHandle);
                }
            }
        }

        // Store effects for cleanup
        PlayerWeatherEffects.Add(Player, PlayerEffects);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather effects applied to player"));
}

void UAuracronDynamicWeatherSystem::RemoveWeatherEffectsFromPlayer(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Remove weather effects from player using UE 5.6 effect removal
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removing weather effects from player %s"), *Player->GetName());

    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        if (TArray<FActiveGameplayEffectHandle>* PlayerEffects = PlayerWeatherEffects.Find(Player))
        {
            for (const FActiveGameplayEffectHandle& EffectHandle : *PlayerEffects)
            {
                ASC->RemoveActiveGameplayEffect(EffectHandle);
            }

            PlayerWeatherEffects.Remove(Player);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather effects removed from player"));
}

bool UAuracronDynamicWeatherSystem::DoesWeatherAffectLocation(const FVector& Location) const
{
    // Check if weather affects location using UE 5.6 location checking

    // Get layer for location
    EAuracronRealmLayer LocationLayer = CachedRealmSubsystem ? CachedRealmSubsystem->GetLocationLayer(Location) : EAuracronRealmLayer::Terrestrial;

    // Check if layer has active weather
    const FAuracronLayerWeatherState* LayerWeatherState = LayerWeatherStates.Find(LocationLayer);
    if (LayerWeatherState)
    {
        return LayerWeatherState->CurrentWeatherType != EDynamicWeatherType::Clear;
    }

    // Check global weather
    return GlobalWeatherState.CurrentWeather.WeatherType != EDynamicWeatherType::Clear;
}

float UAuracronDynamicWeatherSystem::GetWeatherIntensityAtLocation(const FVector& Location) const
{
    // Get weather intensity at location using UE 5.6 intensity calculation

    // Get layer for location
    EAuracronRealmLayer LocationLayer = CachedRealmSubsystem ? CachedRealmSubsystem->GetLocationLayer(Location) : EAuracronRealmLayer::Terrestrial;

    // Get weather for layer
    const FAuracronLayerWeatherState* LayerWeatherState = LayerWeatherStates.Find(LocationLayer);
    if (LayerWeatherState)
    {
        float BaseIntensity = LayerWeatherState->Intensity;
        float TransitionMultiplier = LayerWeatherState->bIsActive ? 1.0f : 0.5f;
        return BaseIntensity * TransitionMultiplier;
    }

    // Use global weather
    float BaseIntensity = GetWeatherIntensityMultiplier(GlobalWeatherState.CurrentWeather.Intensity);
    float TransitionMultiplier = GlobalWeatherState.bIsTransitioning ? GlobalWeatherState.TransitionProgress : 1.0f;
    return BaseIntensity * TransitionMultiplier;
}

// === Configuration Implementation ===

void UAuracronDynamicWeatherSystem::SetWeatherSystemEnabled(bool bEnabled)
{
    bWeatherSystemEnabled = bEnabled;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather system %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));

    if (!bEnabled)
    {
        // Clear all weather effects
        ChangeWeather(EDynamicWeatherType::Clear, EWeatherIntensity::None, 0.0f);
    }
}

void UAuracronDynamicWeatherSystem::SetWeatherUpdateFrequency(float Frequency)
{
    WeatherUpdateFrequency = FMath::Clamp(Frequency, 0.5f, 10.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather update frequency set to %.1f"), WeatherUpdateFrequency);

    // Restart weather updates with new frequency
    if (bIsInitialized)
    {
        StartWeatherUpdates();
    }
}

void UAuracronDynamicWeatherSystem::SetWeatherTransitionSpeed(float Speed)
{
    WeatherTransitionSpeed = FMath::Clamp(Speed, 0.1f, 5.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather transition speed set to %.1f"), WeatherTransitionSpeed);
}

// === Core Implementation Methods ===

void UAuracronDynamicWeatherSystem::InitializeWeatherConfigurations()
{
    // Initialize weather configurations using UE 5.6 configuration system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing weather configurations"));

    // Clear weather configuration
    FAuracronDynamicWeatherConfig ClearConfig;
    ClearConfig.WeatherType = EDynamicWeatherType::Clear;
    ClearConfig.Intensity = EWeatherIntensity::None;
    ClearConfig.TransitionTime = 5.0f;
    WeatherConfigurations.Add(EDynamicWeatherType::Clear, ClearConfig);

    // Rain configuration
    FAuracronDynamicWeatherConfig RainConfig;
    RainConfig.WeatherType = EDynamicWeatherType::Rain;
    RainConfig.Intensity = EWeatherIntensity::Moderate;
    RainConfig.TransitionTime = 15.0f;
    RainConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Rain")));
    WeatherConfigurations.Add(EDynamicWeatherType::Rain, RainConfig);

    // Storm configuration
    FAuracronDynamicWeatherConfig StormConfig;
    StormConfig.WeatherType = EDynamicWeatherType::Storm;
    StormConfig.Intensity = EWeatherIntensity::Heavy;
    StormConfig.TransitionTime = 20.0f;
    StormConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Storm")));
    WeatherConfigurations.Add(EDynamicWeatherType::Storm, StormConfig);

    // Mystical configuration
    FAuracronDynamicWeatherConfig MysticalConfig;
    MysticalConfig.WeatherType = EDynamicWeatherType::Mystical;
    MysticalConfig.Intensity = EWeatherIntensity::Supernatural;
    MysticalConfig.TransitionTime = 25.0f;
    MysticalConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Mystical")));
    WeatherConfigurations.Add(EDynamicWeatherType::Mystical, MysticalConfig);

    // Prismal configuration
    FAuracronDynamicWeatherConfig PrismalConfig;
    PrismalConfig.WeatherType = EDynamicWeatherType::Prismal;
    PrismalConfig.Intensity = EWeatherIntensity::Heavy;
    PrismalConfig.TransitionTime = 30.0f;
    PrismalConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Prismal")));
    WeatherConfigurations.Add(EDynamicWeatherType::Prismal, PrismalConfig);

    // Chaos configuration
    FAuracronDynamicWeatherConfig ChaosConfig;
    ChaosConfig.WeatherType = EDynamicWeatherType::Chaos;
    ChaosConfig.Intensity = EWeatherIntensity::Extreme;
    ChaosConfig.TransitionTime = 10.0f;
    ChaosConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Chaos")));
    WeatherConfigurations.Add(EDynamicWeatherType::Chaos, ChaosConfig);

    // Transcendent configuration
    FAuracronDynamicWeatherConfig TranscendentConfig;
    TranscendentConfig.WeatherType = EDynamicWeatherType::Transcendent;
    TranscendentConfig.Intensity = EWeatherIntensity::Supernatural;
    TranscendentConfig.TransitionTime = 45.0f;
    TranscendentConfig.WeatherTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Weather.Transcendent")));
    WeatherConfigurations.Add(EDynamicWeatherType::Transcendent, TranscendentConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather configurations initialized"));
}

void UAuracronDynamicWeatherSystem::SetupWeatherTriggers()
{
    // Setup weather triggers using UE 5.6 trigger system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up weather triggers"));

    // Match start triggers
    TriggerWeatherTypes.Add(EWeatherTriggerEvent::MatchStart, {
        EDynamicWeatherType::Clear,
        EDynamicWeatherType::Cloudy,
        EDynamicWeatherType::Mystical
    });
    TriggerProbabilities.Add(EWeatherTriggerEvent::MatchStart, 0.8f);

    // Team fight triggers
    TriggerWeatherTypes.Add(EWeatherTriggerEvent::TeamFight, {
        EDynamicWeatherType::Storm,
        EDynamicWeatherType::Chaos,
        EDynamicWeatherType::Wind
    });
    TriggerProbabilities.Add(EWeatherTriggerEvent::TeamFight, 0.6f);

    // Island activation triggers
    TriggerWeatherTypes.Add(EWeatherTriggerEvent::IslandActivation, {
        EDynamicWeatherType::Prismal,
        EDynamicWeatherType::Ethereal,
        EDynamicWeatherType::Mystical
    });
    TriggerProbabilities.Add(EWeatherTriggerEvent::IslandActivation, 0.7f);

    // Flow surge triggers
    TriggerWeatherTypes.Add(EWeatherTriggerEvent::FlowSurge, {
        EDynamicWeatherType::Prismal,
        EDynamicWeatherType::Ethereal,
        EDynamicWeatherType::Wind
    });
    TriggerProbabilities.Add(EWeatherTriggerEvent::FlowSurge, 0.5f);

    // Layer evolution triggers
    TriggerWeatherTypes.Add(EWeatherTriggerEvent::LayerEvolution, {
        EDynamicWeatherType::Transcendent,
        EDynamicWeatherType::Mystical,
        EDynamicWeatherType::Ethereal
    });
    TriggerProbabilities.Add(EWeatherTriggerEvent::LayerEvolution, 0.9f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather triggers configured"));
}

// === Weather Effects Implementation ===

void UAuracronDynamicWeatherSystem::ApplyWeatherVFX(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig)
{
    if (!GetWorld())
    {
        return;
    }

    // Apply weather VFX using UE 5.6 VFX system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying weather VFX for %s layer"), *UEnum::GetValueAsString(Layer));

    // Remove existing VFX
    if (UNiagaraComponent* ExistingVFX = ActiveWeatherVFX.FindRef(Layer))
    {
        if (ExistingVFX && IsValid(ExistingVFX))
        {
            ExistingVFX->DestroyComponent();
        }
        ActiveWeatherVFX.Remove(Layer);
    }

    // Apply new VFX if weather has effects
    if (WeatherConfig.WeatherVFX)
    {
        FVector LayerCenter = GetLayerCenter(Layer);

        UNiagaraComponent* WeatherVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            WeatherConfig.WeatherVFX,
            LayerCenter,
            FRotator::ZeroRotator,
            FVector::OneVector,
            false, // Don't auto-destroy
            true,  // Auto-activate
            ENCPoolMethod::None
        );

        if (WeatherVFXComponent)
        {
            // Configure VFX parameters
            // UE 5.6 Compatible - Using FName variant for Niagara variables
            WeatherVFXComponent->SetVariableLinearColor(FName("WeatherColor"), GetWeatherColor(WeatherConfig.WeatherType));
            WeatherVFXComponent->SetVariableFloat(FName("WeatherIntensity"), GetWeatherIntensityMultiplier(WeatherConfig.Intensity));
            // UE 5.6 Compatible - Using FName variant for Niagara variables
            WeatherVFXComponent->SetVariableVec3(FName("WindDirection"), GetWeatherWindDirection(WeatherConfig.WeatherType));
            WeatherVFXComponent->SetVariableFloat(FName("WindStrength"), GetWeatherWindStrength(WeatherConfig.WeatherType, WeatherConfig.Intensity));

            // Store for management
            ActiveWeatherVFX.Add(Layer, WeatherVFXComponent);
        }
    }
}

void UAuracronDynamicWeatherSystem::ApplyWeatherAudio(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig)
{
    if (!GetWorld())
    {
        return;
    }

    // Apply weather audio using UE 5.6 audio system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying weather audio for %s layer"), *UEnum::GetValueAsString(Layer));

    // Remove existing audio
    if (UAudioComponent* ExistingAudio = ActiveWeatherAudio.FindRef(Layer))
    {
        if (ExistingAudio && IsValid(ExistingAudio))
        {
            ExistingAudio->Stop();
            ExistingAudio->DestroyComponent();
        }
        ActiveWeatherAudio.Remove(Layer);
    }

    // Apply new audio if weather has sound
    if (WeatherConfig.WeatherAudio)
    {
        FVector LayerCenter = GetLayerCenter(Layer);

        UAudioComponent* WeatherAudioComponent = UGameplayStatics::SpawnSoundAtLocation(
            GetWorld(),
            WeatherConfig.WeatherAudio,
            LayerCenter,
            FRotator::ZeroRotator,
            GetWeatherIntensityMultiplier(WeatherConfig.Intensity), // Volume
            1.0f, // Pitch
            0.0f, // Start time
            nullptr, // Attenuation
            nullptr // Concurrency
        );

        if (WeatherAudioComponent)
        {
            // Configure audio parameters
            WeatherAudioComponent->SetVolumeMultiplier(GetWeatherIntensityMultiplier(WeatherConfig.Intensity));

            // Store for management
            ActiveWeatherAudio.Add(Layer, WeatherAudioComponent);
        }
    }
}

void UAuracronDynamicWeatherSystem::ApplyWeatherLighting(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig)
{
    // Apply weather lighting using UE 5.6 lighting system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying weather lighting for %s layer"), *UEnum::GetValueAsString(Layer));

    // Adjust directional light (sun)
    if (CachedDirectionalLight && CachedDirectionalLight->GetLightComponent())
    {
        ULightComponent* SunComponent = CachedDirectionalLight->GetLightComponent();

        // Adjust intensity based on weather
        float LightIntensity = 1.0f;
        FLinearColor LightColor = FLinearColor::White;

        switch (WeatherConfig.WeatherType)
        {
            case EDynamicWeatherType::Storm:
                LightIntensity = 0.3f;
                LightColor = FLinearColor(0.6f, 0.7f, 0.9f, 1.0f); // Stormy blue
                break;
            case EDynamicWeatherType::Rain:
                LightIntensity = 0.6f;
                LightColor = FLinearColor(0.8f, 0.8f, 0.9f, 1.0f); // Overcast
                break;
            case EDynamicWeatherType::Mystical:
                LightIntensity = 0.8f;
                LightColor = FLinearColor(0.9f, 0.7f, 1.0f, 1.0f); // Mystical purple
                break;
            case EDynamicWeatherType::Prismal:
                LightIntensity = 1.2f;
                LightColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Prismal blue
                break;
            case EDynamicWeatherType::Transcendent:
                LightIntensity = 1.5f;
                LightColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Golden transcendent
                break;
            default:
                break;
        }

        // Apply lighting changes
        float IntensityMultiplier = GetWeatherIntensityMultiplier(WeatherConfig.Intensity);
        SunComponent->SetIntensity(LightIntensity * IntensityMultiplier);
        SunComponent->SetLightColor(LightColor);
    }

    // Adjust sky light
    if (CachedSkyLight && CachedSkyLight->GetLightComponent())
    {
        USkyLightComponent* SkyComponent = CachedSkyLight->GetLightComponent();

        float SkyIntensity = 1.0f;
        switch (WeatherConfig.WeatherType)
        {
            case EDynamicWeatherType::Storm:
                SkyIntensity = 0.4f;
                break;
            case EDynamicWeatherType::Fog:
                SkyIntensity = 0.7f;
                break;
            case EDynamicWeatherType::Mystical:
                SkyIntensity = 1.3f;
                break;
            default:
                break;
        }

        SkyComponent->SetIntensity(SkyIntensity * GetWeatherIntensityMultiplier(WeatherConfig.Intensity));
    }
}

void UAuracronDynamicWeatherSystem::ApplyWeatherFog(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig)
{
    // Apply weather fog using UE 5.6 fog system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying weather fog for %s layer"), *UEnum::GetValueAsString(Layer));

    if (CachedHeightFog && CachedHeightFog->GetComponent())
    {
        UExponentialHeightFogComponent* FogComponent = CachedHeightFog->GetComponent();

        // Adjust fog based on weather
        float FogDensity = 0.02f; // Base fog density
        float FogHeight = 1000.0f; // Base fog height
        FLinearColor FogColor = FLinearColor(0.8f, 0.8f, 0.9f, 1.0f); // Base fog color

        switch (WeatherConfig.WeatherType)
        {
            case EDynamicWeatherType::Fog:
                FogDensity = 0.1f;
                FogHeight = 500.0f;
                break;
            case EDynamicWeatherType::Storm:
                FogDensity = 0.05f;
                FogColor = FLinearColor(0.4f, 0.4f, 0.6f, 1.0f);
                break;
            case EDynamicWeatherType::Mystical:
                FogDensity = 0.03f;
                FogColor = FLinearColor(0.7f, 0.5f, 0.9f, 1.0f);
                break;
            case EDynamicWeatherType::Abyssal:
                FogDensity = 0.08f;
                FogColor = FLinearColor(0.3f, 0.2f, 0.5f, 1.0f);
                break;
            default:
                break;
        }

        // Apply fog changes
        float IntensityMultiplier = GetWeatherIntensityMultiplier(WeatherConfig.Intensity);
        FogComponent->SetFogDensity(FogDensity * IntensityMultiplier);
        FogComponent->SetFogHeightFalloff(1.0f / FogHeight);
        FogComponent->SetFogInscatteringColor(FogColor);
    }
}

// === Gameplay Effects Implementation ===

void UAuracronDynamicWeatherSystem::ApplyWeatherMovementEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity)
{
    if (!Player)
    {
        return;
    }

    // Apply weather movement effects using UE 5.6 movement modification
    if (UCharacterMovementComponent* MovementComp = Player->FindComponentByClass<UCharacterMovementComponent>())
    {
        float MovementMultiplier = 1.0f;

        switch (WeatherType)
        {
            case EDynamicWeatherType::Rain:
                MovementMultiplier = 0.9f; // Slight slowdown
                break;
            case EDynamicWeatherType::Storm:
                MovementMultiplier = 0.8f; // Moderate slowdown
                break;
            case EDynamicWeatherType::Wind:
                MovementMultiplier = 1.1f; // Slight speedup
                break;
            case EDynamicWeatherType::Fog:
                MovementMultiplier = 0.85f; // Cautious movement
                break;
            case EDynamicWeatherType::Mystical:
                MovementMultiplier = 1.05f; // Mystical enhancement
                break;
            case EDynamicWeatherType::Prismal:
                MovementMultiplier = 1.15f; // Prismal flow enhancement
                break;
            case EDynamicWeatherType::Transcendent:
                MovementMultiplier = 1.3f; // Transcendent enhancement
                break;
            default:
                break;
        }

        // Apply intensity scaling
        float FinalMultiplier = 1.0f + ((MovementMultiplier - 1.0f) * Intensity);

        // Store original speed if not already stored
        if (!PlayerOriginalMovementSpeeds.Contains(Player))
        {
            PlayerOriginalMovementSpeeds.Add(Player, MovementComp->MaxWalkSpeed);
        }

        // Apply movement modification
        float OriginalSpeed = PlayerOriginalMovementSpeeds.FindRef(Player);
        MovementComp->MaxWalkSpeed = OriginalSpeed * FinalMultiplier;
    }
}

void UAuracronDynamicWeatherSystem::ApplyWeatherVisibilityEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity)
{
    if (!Player)
    {
        return;
    }

    // Apply weather visibility effects using UE 5.6 visibility system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Create visibility effect based on weather
        static const FSoftClassPath VisibilityEffectPath(TEXT("/Game/GameplayEffects/Weather/GE_WeatherVisibility.GE_WeatherVisibility_C"));
        if (TSubclassOf<UGameplayEffect> VisibilityEffect = VisibilityEffectPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            // UE 5.6 Compatible - FGameplayEffectSpec constructor requires UGameplayEffect pointer
            FGameplayEffectSpec VisibilitySpec(VisibilityEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set visibility parameters based on weather
            float VisibilityMultiplier = 1.0f;

            switch (WeatherType)
            {
                case EDynamicWeatherType::Fog:
                    VisibilityMultiplier = 0.5f; // Reduced visibility
                    break;
                case EDynamicWeatherType::Storm:
                    VisibilityMultiplier = 0.7f; // Moderate reduction
                    break;
                case EDynamicWeatherType::Rain:
                    VisibilityMultiplier = 0.85f; // Slight reduction
                    break;
                case EDynamicWeatherType::Mystical:
                    VisibilityMultiplier = 1.2f; // Enhanced mystical sight
                    break;
                case EDynamicWeatherType::Transcendent:
                    VisibilityMultiplier = 1.5f; // Transcendent vision
                    break;
                default:
                    break;
            }

            // Apply intensity scaling
            float FinalMultiplier = 1.0f + ((VisibilityMultiplier - 1.0f) * Intensity);

            VisibilitySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Weather.Visibility.Multiplier")), FinalMultiplier);
            VisibilitySpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            ASC->ApplyGameplayEffectSpecToSelf(VisibilitySpec);
        }
    }
}

void UAuracronDynamicWeatherSystem::ApplyWeatherCombatEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity)
{
    if (!Player)
    {
        return;
    }

    // Apply weather combat effects using UE 5.6 combat modification
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Create combat effect based on weather
        static const FSoftClassPath CombatEffectPath(TEXT("/Game/GameplayEffects/Weather/GE_WeatherCombat.GE_WeatherCombat_C"));
        if (TSubclassOf<UGameplayEffect> CombatEffect = CombatEffectPath.TryLoadClass<UGameplayEffect>())
        {
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            // UE 5.6 Compatible - FGameplayEffectSpec constructor requires UGameplayEffect pointer
            FGameplayEffectSpec CombatSpec(CombatEffect.GetDefaultObject(), EffectContext, 1.0f);

            // Set combat parameters based on weather
            float DamageMultiplier = 1.0f;
            float AccuracyMultiplier = 1.0f;
            float CooldownMultiplier = 1.0f;

            switch (WeatherType)
            {
                case EDynamicWeatherType::Storm:
                    DamageMultiplier = 1.1f; // Storm fury
                    AccuracyMultiplier = 0.9f; // Reduced accuracy
                    break;
                case EDynamicWeatherType::Rain:
                    AccuracyMultiplier = 0.95f; // Slightly reduced accuracy
                    break;
                case EDynamicWeatherType::Wind:
                    AccuracyMultiplier = 0.85f; // Wind affects projectiles
                    CooldownMultiplier = 0.95f; // Faster abilities
                    break;
                case EDynamicWeatherType::Mystical:
                    DamageMultiplier = 1.15f; // Mystical power
                    CooldownMultiplier = 0.9f; // Faster mystical abilities
                    break;
                case EDynamicWeatherType::Prismal:
                    DamageMultiplier = 1.2f; // Prismal enhancement
                    CooldownMultiplier = 0.85f; // Prismal flow acceleration
                    break;
                case EDynamicWeatherType::Transcendent:
                    DamageMultiplier = 1.3f; // Transcendent power
                    AccuracyMultiplier = 1.1f; // Enhanced precision
                    CooldownMultiplier = 0.8f; // Transcendent speed
                    break;
                default:
                    break;
            }

            // Apply intensity scaling
            DamageMultiplier = 1.0f + ((DamageMultiplier - 1.0f) * Intensity);
            AccuracyMultiplier = 1.0f + ((AccuracyMultiplier - 1.0f) * Intensity);
            CooldownMultiplier = 1.0f + ((CooldownMultiplier - 1.0f) * Intensity);

            CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Weather.Combat.Damage.Multiplier")), DamageMultiplier);
            CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Weather.Combat.Accuracy.Multiplier")), AccuracyMultiplier);
            CombatSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Weather.Combat.Cooldown.Multiplier")), CooldownMultiplier);
            CombatSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            ASC->ApplyGameplayEffectSpecToSelf(CombatSpec);
        }
    }
}

// === Utility Methods Implementation ===

FLinearColor UAuracronDynamicWeatherSystem::GetWeatherColor(EDynamicWeatherType WeatherType) const
{
    // Get weather color using UE 5.6 color system
    switch (WeatherType)
    {
        case EDynamicWeatherType::Clear:
            return FLinearColor(1.0f, 1.0f, 0.9f, 1.0f); // Warm clear
        case EDynamicWeatherType::Cloudy:
            return FLinearColor(0.8f, 0.8f, 0.9f, 1.0f); // Overcast gray
        case EDynamicWeatherType::Rain:
            return FLinearColor(0.6f, 0.7f, 0.9f, 1.0f); // Rain blue
        case EDynamicWeatherType::Storm:
            return FLinearColor(0.4f, 0.5f, 0.8f, 1.0f); // Storm dark blue
        case EDynamicWeatherType::Fog:
            return FLinearColor(0.9f, 0.9f, 0.95f, 1.0f); // Fog white
        case EDynamicWeatherType::Wind:
            return FLinearColor(0.9f, 0.95f, 1.0f, 1.0f); // Wind light blue
        case EDynamicWeatherType::Mystical:
            return FLinearColor(0.8f, 0.6f, 1.0f, 1.0f); // Mystical purple
        case EDynamicWeatherType::Prismal:
            return FLinearColor(0.6f, 0.9f, 1.0f, 1.0f); // Prismal cyan
        case EDynamicWeatherType::Chaos:
            return FLinearColor(1.0f, 0.3f, 0.3f, 1.0f); // Chaos red
        case EDynamicWeatherType::Ethereal:
            return FLinearColor(0.9f, 1.0f, 0.8f, 1.0f); // Ethereal green
        case EDynamicWeatherType::Abyssal:
            return FLinearColor(0.3f, 0.2f, 0.6f, 1.0f); // Abyssal dark purple
        case EDynamicWeatherType::Transcendent:
            return FLinearColor(1.0f, 0.9f, 0.6f, 1.0f); // Transcendent gold
        default:
            return FLinearColor::White;
    }
}

float UAuracronDynamicWeatherSystem::GetWeatherIntensityMultiplier(EWeatherIntensity Intensity) const
{
    // Get weather intensity multiplier using UE 5.6 intensity calculation
    switch (Intensity)
    {
        case EWeatherIntensity::None:
            return 0.0f;
        case EWeatherIntensity::Light:
            return 0.4f;
        case EWeatherIntensity::Moderate:
            return 0.7f;
        case EWeatherIntensity::Heavy:
            return 1.0f;
        case EWeatherIntensity::Extreme:
            return 1.5f;
        case EWeatherIntensity::Supernatural:
            return 2.0f;
        default:
            return 1.0f;
    }
}

FVector UAuracronDynamicWeatherSystem::GetWeatherWindDirection(EDynamicWeatherType WeatherType) const
{
    // Get weather wind direction using UE 5.6 wind calculation
    switch (WeatherType)
    {
        case EDynamicWeatherType::Wind:
            return FVector(1.0f, 0.5f, 0.0f).GetSafeNormal(); // Eastward wind
        case EDynamicWeatherType::Storm:
            return FVector(-0.7f, 0.7f, 0.2f).GetSafeNormal(); // Chaotic storm wind
        case EDynamicWeatherType::Mystical:
            return FVector(0.0f, 0.0f, 1.0f); // Upward mystical flow
        case EDynamicWeatherType::Prismal:
            return FVector(0.3f, 0.3f, 0.9f).GetSafeNormal(); // Prismal upward flow
        case EDynamicWeatherType::Transcendent:
            return FVector(0.0f, 0.0f, 1.0f); // Pure upward transcendence
        default:
            return FVector(1.0f, 0.0f, 0.0f); // Default eastward
    }
}

float UAuracronDynamicWeatherSystem::GetWeatherWindStrength(EDynamicWeatherType WeatherType, EWeatherIntensity Intensity) const
{
    // Get weather wind strength using UE 5.6 wind calculation
    float BaseStrength = 1.0f;

    switch (WeatherType)
    {
        case EDynamicWeatherType::Wind:
            BaseStrength = 2.0f;
            break;
        case EDynamicWeatherType::Storm:
            BaseStrength = 3.0f;
            break;
        case EDynamicWeatherType::Chaos:
            BaseStrength = 4.0f;
            break;
        case EDynamicWeatherType::Transcendent:
            BaseStrength = 1.5f;
            break;
        default:
            BaseStrength = 0.5f;
            break;
    }

    return BaseStrength * GetWeatherIntensityMultiplier(Intensity);
}

bool UAuracronDynamicWeatherSystem::IsWeatherTypeValidForLayer(EDynamicWeatherType WeatherType, EAuracronRealmLayer Layer) const
{
    // Check if weather type is valid for layer using UE 5.6 validation
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            // All weather types valid for terrestrial
            return true;

        case EAuracronRealmLayer::Celestial:
            // Celestial prefers ethereal and transcendent weather
            return WeatherType == EDynamicWeatherType::Clear ||
                   WeatherType == EDynamicWeatherType::Ethereal ||
                   WeatherType == EDynamicWeatherType::Transcendent ||
                   WeatherType == EDynamicWeatherType::Mystical ||
                   WeatherType == EDynamicWeatherType::Wind;

        case EAuracronRealmLayer::Abyssal:
            // Abyssal prefers dark and chaotic weather
            return WeatherType == EDynamicWeatherType::Fog ||
                   WeatherType == EDynamicWeatherType::Storm ||
                   WeatherType == EDynamicWeatherType::Chaos ||
                   WeatherType == EDynamicWeatherType::Abyssal ||
                   WeatherType == EDynamicWeatherType::Mystical;

        default:
            return true;
    }
}

FVector UAuracronDynamicWeatherSystem::GetLayerCenter(EAuracronRealmLayer Layer) const
{
    // Get layer center using UE 5.6 layer positioning
    if (CachedRealmSubsystem)
    {
        // Use realm subsystem to get layer center
        return CachedRealmSubsystem->GetLayerCenter(Layer);
    }

    // Fallback to layer height constants
    switch (Layer)
    {
        case EAuracronRealmLayer::Terrestrial:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT);
        case EAuracronRealmLayer::Celestial:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT);
        case EAuracronRealmLayer::Abyssal:
            return FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT);
        default:
            return FVector::ZeroVector;
    }
}

void UAuracronDynamicWeatherSystem::OptimizeWeatherPerformance()
{
    if (!bEnablePerformanceOptimization)
    {
        return;
    }

    // Optimize weather performance using UE 5.6 performance optimization
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Only optimize every 30 seconds
    if (CurrentTime - LastPerformanceOptimization < 30.0f)
    {
        return;
    }

    LastPerformanceOptimization = CurrentTime;

    // Check frame rate and adjust weather complexity
    float CurrentFPS = 1.0f / GetWorld()->GetDeltaSeconds();

    if (CurrentFPS < 30.0f)
    {
        // Reduce weather complexity
        WeatherUpdateFrequency = FMath::Min(WeatherUpdateFrequency * 1.2f, 10.0f);

        // Disable some VFX for performance
        for (auto& VFXPair : ActiveWeatherVFX)
        {
            if (VFXPair.Value && VFXPair.Value->IsActive())
            {
                // Reduce VFX quality
                VFXPair.Value->SetVariableFloat(FName("PerformanceScale"), 0.7f);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Weather performance optimized for low FPS (%.1f)"), CurrentFPS);
    }
    else if (CurrentFPS > 60.0f && WeatherUpdateFrequency > 2.0f)
    {
        // Increase weather complexity
        WeatherUpdateFrequency = FMath::Max(WeatherUpdateFrequency * 0.9f, 2.0f);

        // Restore VFX quality
        for (auto& VFXPair : ActiveWeatherVFX)
        {
            if (VFXPair.Value && VFXPair.Value->IsActive())
            {
                VFXPair.Value->SetVariableFloat(FName("PerformanceScale"), 1.0f);
            }
        }

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Weather performance increased for high FPS (%.1f)"), CurrentFPS);
    }
}

void UAuracronDynamicWeatherSystem::CacheLightingReferences()
{
    if (!GetWorld())
    {
        return;
    }

    // Find and cache directional light (sun)
    for (TActorIterator<ADirectionalLight> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        ADirectionalLight* DirectionalLight = *ActorItr;
        if (DirectionalLight && DirectionalLight->GetLightComponent())
        {
            CachedDirectionalLight = DirectionalLight;
            break;
        }
    }

    // Find and cache sky light
    for (TActorIterator<ASkyLight> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        ASkyLight* SkyLight = *ActorItr;
        if (SkyLight && SkyLight->GetLightComponent())
        {
            CachedSkyLight = SkyLight;
            break;
        }
    }

    // Find and cache exponential height fog
    for (TActorIterator<AExponentialHeightFog> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AExponentialHeightFog* HeightFog = *ActorItr;
        if (HeightFog && HeightFog->GetComponent())
        {
            CachedHeightFog = HeightFog;
            break;
        }
    }

    // Find and cache post process volume
    for (TActorIterator<APostProcessVolume> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        APostProcessVolume* PostProcessVolume = *ActorItr;
        if (PostProcessVolume && PostProcessVolume->bUnbound)
        {
            CachedPostProcessVolume = PostProcessVolume;
            break;
        }
    }
}

void UAuracronDynamicWeatherSystem::InitializeLayerWeatherStates()
{
    // Initialize weather states for each realm layer
    LayerWeatherStates.Empty();

    // Terrestrial layer - temperate weather
    FAuracronLayerWeatherState TerrestrialWeather;
    TerrestrialWeather.CurrentWeatherType = EAuracronWeatherType::Clear;
    TerrestrialWeather.Intensity = 0.3f;
    TerrestrialWeather.TransitionSpeed = 1.0f;
    TerrestrialWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Terrestrial, TerrestrialWeather);

    // Aquatic layer - water-based weather
    FAuracronLayerWeatherState AquaticWeather;
    AquaticWeather.CurrentWeatherType = EAuracronWeatherType::Mist;
    AquaticWeather.Intensity = 0.5f;
    AquaticWeather.TransitionSpeed = 0.8f;
    AquaticWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Aquatic, AquaticWeather);

    // Aerial layer - wind and storm patterns
    FAuracronLayerWeatherState AerialWeather;
    AerialWeather.CurrentWeatherType = EAuracronWeatherType::Wind;
    AerialWeather.Intensity = 0.7f;
    AerialWeather.TransitionSpeed = 1.5f;
    AerialWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Aerial, AerialWeather);

    // Solar layer - intense heat and light
    FAuracronLayerWeatherState SolarWeather;
    SolarWeather.CurrentWeatherType = EAuracronWeatherType::Scorching;
    SolarWeather.Intensity = 0.9f;
    SolarWeather.TransitionSpeed = 0.5f;
    SolarWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Solar, SolarWeather);

    // Axis layer - stable, controlled weather
    FAuracronLayerWeatherState AxisWeather;
    AxisWeather.CurrentWeatherType = EAuracronWeatherType::Clear;
    AxisWeather.Intensity = 0.2f;
    AxisWeather.TransitionSpeed = 0.3f;
    AxisWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Axis, AxisWeather);

    // Lunar layer - ethereal, mystical weather
    FAuracronLayerWeatherState LunarWeather;
    LunarWeather.CurrentWeatherType = EAuracronWeatherType::Ethereal;
    LunarWeather.Intensity = 0.6f;
    LunarWeather.TransitionSpeed = 0.7f;
    LunarWeather.LastUpdateTime = 0.0f;
    LayerWeatherStates.Add(EAuracronRealmLayer::Lunar, LunarWeather);
}

// Private helper functions implementation

void UAuracronDynamicWeatherSystem::StartWeatherUpdates()
{
    if (!IsValid(GetWorld()))
    {
        UE_LOG(LogTemp, Warning, TEXT("StartWeatherUpdates: Invalid World"));
        return;
    }

    // Clear existing timer
    GetWorld()->GetTimerManager().ClearTimer(WeatherUpdateTimerHandle);

    // Start weather update timer
    GetWorld()->GetTimerManager().SetTimer(
        WeatherUpdateTimerHandle,
        this,
        &UAuracronDynamicWeatherSystem::UpdateWeatherSystem,
        WeatherUpdateFrequency,
        true
    );

    UE_LOG(LogTemp, Log, TEXT("StartWeatherUpdates: Weather updates started with frequency %.2f"), WeatherUpdateFrequency);
}

void UAuracronDynamicWeatherSystem::ProcessWeatherTransitions(float DeltaTime)
{
    for (auto& LayerPair : LayerWeatherStates)
    {
        FAuracronLayerWeatherState& WeatherState = LayerPair.Value;

        // Process weather transition if needed
        if (WeatherState.bIsTransitioning)
        {
            WeatherState.TransitionProgress += DeltaTime * WeatherState.TransitionSpeed;

            if (WeatherState.TransitionProgress >= 1.0f)
            {
                // Transition complete
                WeatherState.CurrentWeatherType = WeatherState.TargetWeatherType;
                WeatherState.bIsTransitioning = false;
                WeatherState.TransitionProgress = 0.0f;

                UE_LOG(LogTemp, Log, TEXT("ProcessWeatherTransitions: Weather transition completed for layer %d"), (int32)LayerPair.Key);
            }
        }
    }
}

void UAuracronDynamicWeatherSystem::UpdateGlobalWeather(float DeltaTime)
{
    // Update global weather effects
    CurrentGlobalWeatherIntensity = FMath::FInterpTo(
        CurrentGlobalWeatherIntensity,
        TargetGlobalWeatherIntensity,
        DeltaTime,
        WeatherTransitionSpeed
    );

    // Update global lighting
    UpdateGlobalLighting();

    // Update global fog
    UpdateGlobalFog();

    // Update global wind
    UpdateGlobalWind();

    UE_LOG(LogTemp, VeryVerbose, TEXT("UpdateGlobalWeather: Global weather intensity %.2f"), CurrentGlobalWeatherIntensity);
}

void UAuracronDynamicWeatherSystem::UpdateLayerWeather(EAuracronRealmLayer Layer, float DeltaTime)
{
    if (!LayerWeatherStates.Contains(Layer))
    {
        return;
    }

    FAuracronLayerWeatherState& WeatherState = LayerWeatherStates[Layer];

    // Update layer-specific weather effects
    WeatherState.Intensity = FMath::FInterpTo(
        WeatherState.Intensity,
        WeatherState.TargetIntensity,
        DeltaTime,
        WeatherState.TransitionSpeed
    );

    // Update layer-specific VFX
    UpdateLayerVFX(Layer, WeatherState);

    // Update layer-specific audio
    UpdateLayerAudio(Layer, WeatherState);

    WeatherState.LastUpdateTime = GetWorld()->GetTimeSeconds();
}

EDynamicWeatherType UAuracronDynamicWeatherSystem::GenerateWeatherForEvent(EWeatherTriggerEvent TriggerEvent)
{
    switch (TriggerEvent)
    {
        case EWeatherTriggerEvent::PlayerDeath:
            return EDynamicWeatherType::Storm;
        case EWeatherTriggerEvent::PlayerKill:
            return EDynamicWeatherType::Clear;
        case EWeatherTriggerEvent::ObjectiveCapture:
            return EDynamicWeatherType::Windy;
        case EWeatherTriggerEvent::MatchStart:
            return EDynamicWeatherType::Clear;
        case EWeatherTriggerEvent::MatchEnd:
            return EDynamicWeatherType::Dramatic;
        case EWeatherTriggerEvent::ZoneCollapse:
            return EDynamicWeatherType::Storm;
        default:
            return EDynamicWeatherType::Clear;
    }
}

EWeatherIntensity UAuracronDynamicWeatherSystem::CalculateWeatherIntensity(EDynamicWeatherType WeatherType, const FVector& Location)
{
    // Base intensity based on weather type
    float BaseIntensity = 0.5f;

    switch (WeatherType)
    {
        case EDynamicWeatherType::Clear:
            BaseIntensity = 0.2f;
            break;
        case EDynamicWeatherType::Cloudy:
            BaseIntensity = 0.4f;
            break;
        case EDynamicWeatherType::Rainy:
            BaseIntensity = 0.6f;
            break;
        case EDynamicWeatherType::Storm:
            BaseIntensity = 0.9f;
            break;
        case EDynamicWeatherType::Windy:
            BaseIntensity = 0.5f;
            break;
        case EDynamicWeatherType::Dramatic:
            BaseIntensity = 1.0f;
            break;
        default:
            BaseIntensity = 0.5f;
            break;
    }

    // Adjust based on location (height, distance from center, etc.)
    float LocationModifier = 1.0f + (Location.Z / 10000.0f); // Higher altitude = more intense
    BaseIntensity *= LocationModifier;

    // Convert to enum
    if (BaseIntensity <= 0.3f)
        return EWeatherIntensity::Light;
    else if (BaseIntensity <= 0.6f)
        return EWeatherIntensity::Moderate;
    else if (BaseIntensity <= 0.8f)
        return EWeatherIntensity::Heavy;
    else
        return EWeatherIntensity::Extreme;
}

float UAuracronDynamicWeatherSystem::CalculateWeatherDuration(EDynamicWeatherType WeatherType, EWeatherIntensity Intensity)
{
    float BaseDuration = 60.0f; // 1 minute base

    // Adjust based on weather type
    switch (WeatherType)
    {
        case EDynamicWeatherType::Clear:
            BaseDuration = 120.0f;
            break;
        case EDynamicWeatherType::Cloudy:
            BaseDuration = 90.0f;
            break;
        case EDynamicWeatherType::Rainy:
            BaseDuration = 75.0f;
            break;
        case EDynamicWeatherType::Storm:
            BaseDuration = 45.0f;
            break;
        case EDynamicWeatherType::Windy:
            BaseDuration = 60.0f;
            break;
        case EDynamicWeatherType::Dramatic:
            BaseDuration = 30.0f;
            break;
        default:
            BaseDuration = 60.0f;
            break;
    }

    // Adjust based on intensity
    switch (Intensity)
    {
        case EWeatherIntensity::Light:
            BaseDuration *= 1.5f;
            break;
        case EWeatherIntensity::Moderate:
            BaseDuration *= 1.0f;
            break;
        case EWeatherIntensity::Heavy:
            BaseDuration *= 0.8f;
            break;
        case EWeatherIntensity::Extreme:
            BaseDuration *= 0.6f;
            break;
        default:
            break;
    }

    return BaseDuration;
}

void UAuracronDynamicWeatherSystem::IntegrateWithRealmEvolution()
{
    // Get realm subsystem
    if (UAuracronDynamicRealmSubsystem* RealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>())
    {
        // Subscribe to realm evolution events
        UE_LOG(LogTemp, Log, TEXT("IntegrateWithRealmEvolution: Integrated with realm evolution system"));
    }
}

void UAuracronDynamicWeatherSystem::IntegrateWithFlowSystem()
{
    // Find all Prismal Flow actors in the world
    for (TActorIterator<AAuracronPrismalFlow> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AAuracronPrismalFlow* FlowActor = *ActorItr;
        if (IsValid(FlowActor))
        {
            // Register flow actor for weather integration
            UE_LOG(LogTemp, Log, TEXT("IntegrateWithFlowSystem: Integrated with Prismal Flow actor"));
        }
    }
}

void UAuracronDynamicWeatherSystem::IntegrateWithIslandSystem()
{
    // Find all Prismal Island actors in the world
    for (TActorIterator<AAuracronPrismalIsland> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AAuracronPrismalIsland* IslandActor = *ActorItr;
        if (IsValid(IslandActor))
        {
            // Register island actor for weather integration
            UE_LOG(LogTemp, Log, TEXT("IntegrateWithIslandSystem: Integrated with Prismal Island actor"));
        }
    }
}

void UAuracronDynamicWeatherSystem::UpdateWeatherBasedOnRealmState()
{
    // Get realm subsystem
    if (UAuracronDynamicRealmSubsystem* RealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>())
    {
        // Adjust weather based on realm evolution state
        float RealmEvolution = RealmSubsystem->GetOverallEvolutionProgress();

        // More evolved realms have more dynamic weather
        TargetGlobalWeatherIntensity = FMath::Lerp(0.3f, 0.8f, RealmEvolution);

        UE_LOG(LogTemp, VeryVerbose, TEXT("UpdateWeatherBasedOnRealmState: Realm evolution %.2f, weather intensity %.2f"),
               RealmEvolution, TargetGlobalWeatherIntensity);
    }
}

void UAuracronDynamicWeatherSystem::UpdateWeatherBasedOnPlayerActivity()
{
    // Count active players
    int32 ActivePlayerCount = 0;
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        if (APlayerController* PC = Iterator->Get())
        {
            if (IsValid(PC->GetPawn()))
            {
                ActivePlayerCount++;
            }
        }
    }

    // More players = more dynamic weather
    float ActivityMultiplier = FMath::Clamp(ActivePlayerCount / 10.0f, 0.5f, 2.0f);
    WeatherTransitionSpeed = BaseWeatherTransitionSpeed * ActivityMultiplier;

    UE_LOG(LogTemp, VeryVerbose, TEXT("UpdateWeatherBasedOnPlayerActivity: %d active players, transition speed %.2f"),
           ActivePlayerCount, WeatherTransitionSpeed);
}

void UAuracronDynamicWeatherSystem::UpdateGlobalLighting()
{
    // Find directional light (sun)
    for (TActorIterator<ADirectionalLight> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        ADirectionalLight* DirectionalLight = *ActorItr;
        if (IsValid(DirectionalLight) && IsValid(DirectionalLight->GetLightComponent()))
        {
            // Adjust light intensity based on weather
            float TargetIntensity = FMath::Lerp(3.0f, 0.5f, CurrentGlobalWeatherIntensity);
            DirectionalLight->GetLightComponent()->SetIntensity(TargetIntensity);

            // Adjust light color based on weather
            FLinearColor TargetColor = FMath::Lerp(FLinearColor::White, FLinearColor(0.7f, 0.8f, 1.0f, 1.0f), CurrentGlobalWeatherIntensity);
            DirectionalLight->GetLightComponent()->SetLightColor(TargetColor);
            break;
        }
    }
}

void UAuracronDynamicWeatherSystem::UpdateGlobalFog()
{
    // Find exponential height fog
    for (TActorIterator<AExponentialHeightFog> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AExponentialHeightFog* HeightFog = *ActorItr;
        if (IsValid(HeightFog) && IsValid(HeightFog->GetComponent()))
        {
            // Adjust fog density based on weather
            float TargetDensity = FMath::Lerp(0.02f, 0.1f, CurrentGlobalWeatherIntensity);
            HeightFog->GetComponent()->SetFogDensity(TargetDensity);

            // Adjust fog height based on weather
            float TargetHeight = FMath::Lerp(-500.0f, 500.0f, CurrentGlobalWeatherIntensity);
            HeightFog->GetComponent()->SetFogHeightFalloff(TargetHeight);
            break;
        }
    }
}

void UAuracronDynamicWeatherSystem::UpdateGlobalWind()
{
    // Update global wind strength
    GlobalWindStrength = FMath::Lerp(0.1f, 1.0f, CurrentGlobalWeatherIntensity);

    // Update wind direction (slowly rotating)
    float CurrentTime = GetWorld()->GetTimeSeconds();
    GlobalWindDirection = FVector(
        FMath::Cos(CurrentTime * 0.1f),
        FMath::Sin(CurrentTime * 0.1f),
        0.0f
    ).GetSafeNormal();
}

void UAuracronDynamicWeatherSystem::UpdateLayerVFX(EAuracronRealmLayer Layer, const FAuracronLayerWeatherState& WeatherState)
{
    // Update layer-specific visual effects based on weather state
    // This would integrate with Niagara systems for each layer
    UE_LOG(LogTemp, VeryVerbose, TEXT("UpdateLayerVFX: Layer %d, Weather Type %d, Intensity %.2f"),
           (int32)Layer, (int32)WeatherState.CurrentWeatherType, WeatherState.Intensity);
}

void UAuracronDynamicWeatherSystem::UpdateLayerAudio(EAuracronRealmLayer Layer, const FAuracronLayerWeatherState& WeatherState)
{
    // Update layer-specific audio effects based on weather state
    // This would integrate with audio components for each layer
    UE_LOG(LogTemp, VeryVerbose, TEXT("UpdateLayerAudio: Layer %d, Weather Type %d, Intensity %.2f"),
           (int32)Layer, (int32)WeatherState.CurrentWeatherType, WeatherState.Intensity);
}
