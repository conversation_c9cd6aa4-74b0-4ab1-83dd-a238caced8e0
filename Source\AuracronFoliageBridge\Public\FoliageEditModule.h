// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "FoliageType.h"
#include "InstancedFoliage.h"
#include "AuracronFoliage.h"

#include "FoliageEditModule.generated.h"

/**
 * Enum for foliage edit modes
 */
UENUM(BlueprintType)
enum class EFoliageEditMode : uint8
{
    Paint       UMETA(DisplayName = "Paint"),
    Erase       UMETA(DisplayName = "Erase"),
    Select      UMETA(DisplayName = "Select"),
    Reapply     UMETA(DisplayName = "Reapply")
};

/**
 * Enum for foliage edit tool types
 */
UENUM(BlueprintType)
enum class EFoliageEditToolType : uint8
{
    Brush       UMETA(DisplayName = "Brush"),
    Lasso       UMETA(DisplayName = "Lasso"),
    Fill        UMETA(DisplayName = "Fill")
};

/**
 * Struct for foliage edit settings
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FFoliageEditSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float BrushSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float PaintDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float EraseDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    bool bAlignToNormal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    bool bRandomYaw;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    FVector2D ScaleRange;

    FFoliageEditSettings()
    {
        BrushSize = 512.0f;
        PaintDensity = 1.0f;
        EraseDensity = 1.0f;
        bAlignToNormal = true;
        bRandomYaw = true;
        ScaleRange = FVector2D(0.9f, 1.1f);
    }
};

/**
 * Auracron Foliage Edit Utility Class
 */
UCLASS(BlueprintType)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageEditUtility : public UObject
{
    GENERATED_BODY()

public:
    UAuracronFoliageEditUtility();

    // Foliage Editing Functions
    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool PaintFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, const FFoliageEditSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool EraseFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, const FFoliageEditSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static TArray<FAuracronFoliageInstanceData> SelectFoliage(UWorld* World, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool ReapplyFoliage(UWorld* World, UFoliageType* FoliageType, const TArray<FAuracronFoliageInstanceData>& Instances);

    // Foliage Type Management
    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static UFoliageType* CreateFoliageType(UStaticMesh* StaticMesh);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool AddFoliageType(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool RemoveFoliageType(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static TArray<UFoliageType*> GetFoliageTypes(UWorld* World);

    // Foliage Instance Management
    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static int32 GetFoliageInstanceCount(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static TArray<FAuracronFoliageInstanceData> GetFoliageInstances(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool UpdateFoliageInstances(UWorld* World, UFoliageType* FoliageType, const TArray<FAuracronFoliageInstanceData>& Instances);

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static bool IsValidFoliageLocation(UWorld* World, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static FVector GetSurfaceNormal(UWorld* World, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Foliage Edit")
    static float GetSurfaceHeight(UWorld* World, const FVector& Location);
};

/**
 * Auracron Foliage Edit Module Interface
 */
class AURACRONFOLIAGEBRIDGE_API IAuracronFoliageEditModule : public IModuleInterface
{
public:
    /**
     * Singleton-like access to this module's interface. This is just for convenience!
     * Beware of calling this during the shutdown phase, though. Your module might have been unloaded already.
     *
     * @return Returns singleton instance, loading the module on demand if needed
     */
    static inline IAuracronFoliageEditModule& Get()
    {
        return FModuleManager::LoadModuleChecked<IAuracronFoliageEditModule>("AuracronFoliageBridge");
    }

    /**
     * Checks to see if this module is loaded and ready. It is only valid to call Get() if IsAvailable() returns true.
     *
     * @return True if the module is loaded and ready to use
     */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronFoliageBridge");
    }

    // Module Interface
    virtual void StartupModule() override {}
    virtual void ShutdownModule() override {}

    // Foliage Edit Interface
    virtual UAuracronFoliageEditUtility* GetFoliageEditUtility() = 0;
    virtual void SetCurrentEditMode(EFoliageEditMode EditMode) = 0;
    virtual EFoliageEditMode GetCurrentEditMode() const = 0;
    virtual void SetCurrentToolType(EFoliageEditToolType ToolType) = 0;
    virtual EFoliageEditToolType GetCurrentToolType() const = 0;
};